{% extends "base.html" %}

{% block content %}
<div class="main-card">
    <div class="card-header">
        <h1 class="card-title">Upload Your Resume</h1>
        <p class="card-subtitle">Upload your resume for AI-powered parsing and job matching</p>
    </div>

    <form action="{{ url_for('upload_resume') }}" method="post" enctype="multipart/form-data" id="uploadForm">
        <div class="upload-area" id="uploadArea">
            <div class="upload-icon">
                <i class="fas fa-cloud-upload-alt" style="font-size: 32px; color: #94a3b8; margin-bottom: 12px;"></i>
            </div>
            <p style="font-size: 16px; color: #334155; margin-bottom: 6px;">
                Drop your resume here, or 
                <label for="resume" style="color: #4f46e5; cursor: pointer; text-decoration: underline;">browse</label>
            </p>
            <p style="color: #64748b; font-size: 13px;">Supports PDF, DOC, DOCX files • AI-powered parsing</p>
            <input type="file" id="resume" name="resume" accept=".pdf,.doc,.docx,.txt" style="display: none;" required>
        </div>

        <div class="text-center" style="margin-top: 20px;">
            <button type="button" class="btn btn-secondary" onclick="document.getElementById('resume').click()">
                <i class="fas fa-file"></i>
                Select Resume File
            </button>
        </div>
    </form>

    <!-- Previous Resumes -->
    {% if resumes %}
    <div style="margin-top: 32px; padding-top: 24px; border-top: 1px solid #e2e8f0;">
        <h3 style="font-size: 16px; font-weight: 600; color: #334155; margin-bottom: 16px;">
            <i class="fas fa-history"></i>
            Previous Uploads
        </h3>
        
        <div style="display: flex; flex-direction: column; gap: 12px;">
            {% for resume in resumes %}
            <div style="background: #f8fafc; padding: 16px; border-radius: 8px; display: flex; justify-content: between; align-items: center;">
                <div style="flex: 1;">
                    <h4 style="font-weight: 600; color: #334155; margin-bottom: 4px;">{{ resume.filename }}</h4>
                    <div style="display: flex; gap: 12px; font-size: 12px; color: #6b7280;">
                        <span>{{ (resume.file_size / 1024 / 1024)|round(1) }} MB</span>
                        <span>•</span>
                        <span>{{ resume.uploaded_at|strftime('%Y-%m-%d %H:%M') }}</span>
                        <span>•</span>
                        <span style="color: {% if resume.parsing_status == 'completed' %}#10b981{% elif resume.parsing_status == 'processing' %}#f59e0b{% elif resume.parsing_status == 'failed' %}#ef4444{% else %}#6b7280{% endif %};">
                            {% if resume.parsing_status == 'completed' %}
                                ✅ Parsed
                            {% elif resume.parsing_status == 'processing' %}
                                🔄 Processing
                            {% elif resume.parsing_status == 'failed' %}
                                ❌ Failed
                            {% else %}
                                ⏳ Pending
                            {% endif %}
                        </span>
                    </div>
                </div>
                
                {% if resume.parsing_status == 'completed' %}
                <div style="margin-left: 16px;">
                    <span style="background: #d1fae5; color: #065f46; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">
                        Data Extracted
                    </span>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <div class="text-center" style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e2e8f0;">
        <p style="color: #64748b; margin-bottom: 12px; font-size: 14px;">Or skip upload and use demo data</p>
        <a href="{{ url_for('resume_based_profile') }}" class="btn btn-primary">
            <i class="fas fa-play"></i>
            Use Demo Data
        </a>
    </div>
</div>

<!-- How It Works Section -->
<div class="main-card" style="margin-top: 20px;">
    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 20px;">
        <i class="fas fa-magic" style="color: #4f46e5; font-size: 16px;"></i>
        <h3 style="font-size: 16px; font-weight: 600; color: #334155;">AI-Powered Resume Processing</h3>
    </div>
    
    <div class="how-it-works-grid">
        <div class="step-item">
            <div class="step-icon">
                <span>1</span>
            </div>
            <p class="step-text">Upload Resume</p>
            <p class="step-desc">PDF, DOC, or DOCX</p>
        </div>
        
        <div class="step-item">
            <div class="step-icon">
                <span>2</span>
            </div>
            <p class="step-text">AI Parsing</p>
            <p class="step-desc">Extract skills & experience</p>
        </div>
        
        <div class="step-item">
            <div class="step-icon">
                <span>3</span>
            </div>
            <p class="step-text">Profile Setup</p>
            <p class="step-desc">Auto-fill your profile</p>
        </div>
        
        <div class="step-item">
            <div class="step-icon">
                <span>4</span>
            </div>
            <p class="step-text">Job Matching</p>
            <p class="step-desc">Find relevant positions</p>
        </div>
    </div>
</div>

<style>
.upload-area {
    border: 2px dashed #cbd5e1;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.2s;
    cursor: pointer;
    background: #fafafa;
}

.upload-area:hover {
    border-color: #4f46e5;
    background: #f8fafc;
}

.upload-area.dragover {
    border-color: #4f46e5;
    background: #eff6ff;
}

.how-it-works-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    text-align: center;
}

.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.step-icon {
    width: 40px;
    height: 40px;
    background: #4f46e5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 16px;
}

.step-text {
    color: #334155;
    font-size: 14px;
    font-weight: 500;
    margin: 0;
}

.step-desc {
    color: #64748b;
    font-size: 12px;
    margin: 0;
}

@media (max-width: 768px) {
    .how-it-works-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }
}

@media (max-width: 480px) {
    .how-it-works-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
}
</style>

<script>
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('resume');
const uploadForm = document.getElementById('uploadForm');

uploadArea.addEventListener('click', () => fileInput.click());

uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        fileInput.files = files;
        uploadForm.submit();
    }
});

fileInput.addEventListener('change', () => {
    if (fileInput.files.length > 0) {
        uploadForm.submit();
    }
});
</script>
{% endblock %}
