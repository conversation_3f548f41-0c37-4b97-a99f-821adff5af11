from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_login import Lo<PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user, UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
import re
import sqlite3
import asyncio
import threading
from datetime import datetime
from utils.resume_parser import EnhancedResumeParser
from utils.browser_automation import AsyncJobAutomationManager
import json
from models.job_profile import JobProfile
from utils.ai_resume_parser import AIResumeParser
import logging

# Import database and models from the models package
from models.database import db, init_db
from models.auth import User, UserProfile
from models.resume import Resume
from models.job import Job
from models.application import Application
from models.website import JobWebsite

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Create upload directory
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize database
init_db(app)

# Initialize login manager
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page.'

# Initialize automation manager
#automation_manager = JobAutomationManager()

# Database migration helper
def migrate_database():
    """Add missing columns and tables to existing database"""
    try:
        # Get database path from app config
        database_uri = app.config['SQLALCHEMY_DATABASE_URI']
        database_path = database_uri.replace('sqlite:///', '')
        conn = sqlite3.connect(database_path)
        cursor = conn.cursor()
        
        # Create job_profiles table if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS job_profiles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL UNIQUE,
                first_name VARCHAR(100),
                middle_name VARCHAR(100),
                last_name VARCHAR(100),
                full_name VARCHAR(255),
                date_of_birth DATE,
                nationality VARCHAR(100),
                gender VARCHAR(20),
                marital_status VARCHAR(50),
                email VARCHAR(255),
                phone_primary VARCHAR(50),
                phone_secondary VARCHAR(50),
                address_line1 VARCHAR(255),
                address_line2 VARCHAR(255),
                city VARCHAR(100),
                state_province VARCHAR(100),
                postal_code VARCHAR(20),
                country VARCHAR(100),
                professional_summary TEXT,
                career_objective TEXT,
                years_of_experience INTEGER,
                current_position VARCHAR(255),
                current_company VARCHAR(255),
                current_salary VARCHAR(100),
                expected_salary_min INTEGER,
                expected_salary_max INTEGER,
                salary_currency VARCHAR(10) DEFAULT 'USD',
                technical_skills TEXT,
                soft_skills TEXT,
                programming_languages TEXT,
                frameworks_libraries TEXT,
                databases TEXT,
                tools_software TEXT,
                operating_systems TEXT,
                cloud_platforms TEXT,
                methodologies TEXT,
                languages_spoken TEXT,
                work_experience TEXT,
                education TEXT,
                certifications TEXT,
                projects TEXT,
                publications TEXT,
                research_experience TEXT,
                awards TEXT,
                achievements TEXT,
                professional_memberships TEXT,
                volunteer_experience TEXT,
                references TEXT,
                preferred_job_titles TEXT,
                preferred_industries TEXT,
                preferred_company_sizes TEXT,
                preferred_locations TEXT,
                remote_work_preference VARCHAR(50),
                job_type_preference TEXT,
                availability_start_date DATE,
                willing_to_relocate BOOLEAN DEFAULT 0,
                willing_to_travel VARCHAR(50),
                visa_status VARCHAR(100),
                work_authorization VARCHAR(255),
                linkedin_url VARCHAR(500),
                github_url VARCHAR(500),
                portfolio_url VARCHAR(500),
                personal_website VARCHAR(500),
                twitter_url VARCHAR(500),
                stackoverflow_url VARCHAR(500),
                behance_url VARCHAR(500),
                dribbble_url VARCHAR(500),
                hobbies_interests TEXT,
                additional_information TEXT,
                cover_letter_template TEXT,
                profile_completion_score REAL DEFAULT 0.0,
                last_updated_from_resume DATETIME,
                auto_update_enabled BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # Check if users table exists and add missing columns
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if cursor.fetchone():
            cursor.execute("PRAGMA table_info(users)")
            columns = [row[1] for row in cursor.fetchall()]
            
            missing_columns = [
                ('skills', 'TEXT'),
                ('experience', 'TEXT'),
                ('preferred_titles', 'TEXT'),
                ('preferred_locations', 'TEXT'),
                ('resume_parsed', 'BOOLEAN DEFAULT 0'),
                ('automation_status', 'TEXT DEFAULT "idle"')
            ]
            
            for column_name, column_type in missing_columns:
                if column_name not in columns:
                    try:
                        cursor.execute(f"ALTER TABLE users ADD COLUMN {column_name} {column_type}")
                        print(f"✅ Added column: {column_name}")
                    except sqlite3.OperationalError as e:
                        if "duplicate column name" not in str(e):
                            print(f"⚠️  Warning adding {column_name}: {e}")
        
        conn.commit()
        conn.close()
        print("✅ Database migration completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Migration error: {e}")
        return False

# User loader for Flask-Login
@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Helper functions
def is_valid_email(email):
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def is_strong_password(password):
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    if not re.search(r'[A-Z]', password):
        return False, "Password must contain at least one uppercase letter"
    if not re.search(r'[a-z]', password):
        return False, "Password must contain at least one lowercase letter"
    if not re.search(r'\d', password):
        return False, "Password must contain at least one number"
    return True, "Password is strong"

def allowed_file(filename):
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'txt'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def create_default_websites(user_id):
    """Create default websites for user"""
    # Use the model's built-in method
    return JobWebsite.create_defaults_for_user(user_id)

def run_automation_async(user_id):
    """Run automation in background thread using real browser automation"""
    def automation_worker():
        try:
            with app.app_context():
                user = User.query.get(user_id)
                if not user:
                    return
                
                # Update status
                user.automation_status = 'running'
                db.session.commit()
                
                # Get user profile data
                user_profile = {
                    'full_name': user.full_name or 'User',
                    'email': user.email,
                    'phone': user.phone or '',
                    'location': user.location or 'Remote'
                }
                
                # Get user preferences
                preferences = {
                    'skills': user.skills or 'Python, JavaScript',
                    'preferred_titles': user.preferred_titles or 'Developer, Engineer',
                    'preferred_locations': user.preferred_locations or 'Remote',
                    'experience': user.experience or ''
                }
                
                # Get active websites
                websites = JobWebsite.query.filter_by(user_id=user_id, active=True).all()
                websites_data = [
                    {
                        'name': w.name,
                        'url': w.url,
                        'active': w.active
                    }
                    for w in websites
                ]
                
                # Run real browser automation
                automation_manager = AsyncJobAutomationManager()
                results = automation_manager.run_automation_sync(user_profile, websites_data, preferences)
                
                if results['success']:
                    # Save jobs and applications to database
                    for app_data in results['applications']:
                        # Find the website
                        website = next((w for w in websites if w.name == app_data.get('website', 'Unknown')), websites[0] if websites else None)

                        # Create job record first
                        job = Job.create_from_scrape(
                            user_id=user_id,
                            website_id=website.id if website else None,
                            job_data={
                                'title': app_data.get('title', 'Unknown'),
                                'company': app_data.get('company', 'Unknown'),
                                'job_url': app_data.get('url', ''),
                                'description': app_data.get('description', ''),
                                'salary_range': app_data.get('salary', ''),
                                'location': app_data.get('location', ''),
                                'job_type': app_data.get('job_type'),
                                'remote_type': app_data.get('remote_type'),
                                'external_id': app_data.get('external_id'),
                                'tags': app_data.get('tags', [])
                            }
                        )

                        # Create application record
                        if app_data.get('applied', False):
                            Application.create_from_automation(
                                user_id=user_id,
                                job_id=job.id,
                                form_data=app_data.get('form_data', {}),
                                automation_log=app_data.get('log', [])
                            )

                    # Update website stats
                    for website in websites:
                        website.update_scrape_info()

                    # Update user status
                    user.automation_status = 'completed'
                    db.session.commit()
                    
                    print(f"✅ Automation completed for user {user_id}: {results['total_found']} jobs found, {results['total_applied']} applied")
                else:
                    user.automation_status = 'error'
                    db.session.commit()
                    print(f"❌ Automation failed for user {user_id}: {results.get('error', 'Unknown error')}")
                
        except Exception as e:
            with app.app_context():
                user = User.query.get(user_id)
                if user:
                    user.automation_status = 'error'
                    db.session.commit()
            print(f"❌ Automation error for user {user_id}: {e}")
    
    thread = threading.Thread(target=automation_worker)
    thread.daemon = True
    thread.start()

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('upload'))
    return redirect(url_for('login'))

@app.route('/signup', methods=['GET', 'POST'])
def signup():
    if current_user.is_authenticated:
        return redirect(url_for('upload'))
    
    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')
        full_name = request.form.get('full_name', '').strip()
        
        # Validation
        if not email or not password or not full_name:
            flash('All fields are required', 'error')
            return render_template('auth/signup.html')
        
        if not is_valid_email(email):
            flash('Please enter a valid email address', 'error')
            return render_template('auth/signup.html')
        
        if password != confirm_password:
            flash('Passwords do not match', 'error')
            return render_template('auth/signup.html')
        
        is_strong, message = is_strong_password(password)
        if not is_strong:
            flash(message, 'error')
            return render_template('auth/signup.html')
        
        # Check if user already exists
        if User.query.filter_by(email=email).first():
            flash('Email address already registered', 'error')
            return render_template('auth/signup.html')
        
        try:
            # Create new user
            user = User(email=email, full_name=full_name)
            user.set_password(password)
            
            db.session.add(user)
            db.session.commit()
            
            # Create default websites
            create_default_websites(user.id)
            
            # Log in the user
            login_user(user)
            user.record_login()
            
            flash('Account created successfully! Welcome to AI Job Application Assistant.', 'success')
            return redirect(url_for('upload'))
            
        except Exception as e:
            db.session.rollback()
            flash('An error occurred while creating your account. Please try again.', 'error')
            return render_template('auth/signup.html')
    
    return render_template('auth/signup.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('upload'))
    
    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        remember = bool(request.form.get('remember'))
        
        if not email or not password:
            flash('Email and password are required', 'error')
            return render_template('auth/login.html')
        
        user = User.query.filter_by(email=email).first()
        
        if user and user.check_password(password):
            if not user.is_active:
                flash('Your account has been deactivated. Please contact support.', 'error')
                return render_template('auth/login.html')
            
            login_user(user, remember=remember)
            user.record_login()
            
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            
            flash(f'Welcome back, {user.full_name}!', 'success')
            return redirect(url_for('upload'))
        else:
            flash('Invalid email or password', 'error')
    
    return render_template('auth/login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('You have been logged out successfully', 'info')
    return redirect(url_for('login'))

@app.route('/upload')
@login_required
def upload():
    # Get user's resumes
    resumes = Resume.query.filter_by(user_id=current_user.id).order_by(Resume.uploaded_at.desc()).all()
    return render_template('upload.html', current_page='upload', resumes=resumes)

@app.route('/upload-resume', methods=['POST'])
@login_required
def upload_resume():
    if 'resume' not in request.files:
        flash('No file selected', 'error')
        return redirect(url_for('upload'))
    
    file = request.files['resume']
    if not file.filename or file.filename == '':
        flash('No file selected', 'error')
        return redirect(url_for('upload'))
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename) if file.filename else 'unknown_file'
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safe_filename = f"{current_user.id}_{timestamp}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], safe_filename)
        file.save(file_path)
        
        # Save resume record
        resume = Resume(
            user_id=current_user.id,
            filename=filename,
            file_path=file_path,
            file_size=os.path.getsize(file_path),
            parsing_status='pending'
        )
        db.session.add(resume)
        db.session.commit()
        
        # Parse resume in background
        def parse_resume_async():
            try:
                with app.app_context():
                    resume.parsing_status = 'processing'
                    db.session.commit()
                    
                    # Use AI-powered parser
                    parser = AIResumeParser()
                    parsed_data = parser.parse_resume(file_path)
                    
                    if parsed_data:
                        print(f"🤖 AI Resume parsing successful for user {current_user.id}")
                        
                        # Get or create job profile
                        job_profile = JobProfile.get_or_create_for_user(current_user.id)
                        
                        # Update job profile from parsed data
                        if job_profile.auto_update_enabled:
                            success = job_profile.update_from_resume_data(parsed_data)
                            if success:
                                print(f"✅ Job profile updated from resume data")
                            else:
                                print(f"⚠️ Failed to update job profile from resume data")
                
                        # Save parsed data to resume record
                        resume.parsed_data = json.dumps(parsed_data)
                        resume.parsing_status = 'completed'
                
                        db.session.commit()
                        print(f"✅ Resume parsing and profile update completed")
                
                    else:
                        resume.parsing_status = 'failed'
                        print(f"❌ Resume parsing failed for user {current_user.id}")
                        db.session.commit()
                
            except Exception as e:
                with app.app_context():
                    resume.parsing_status = 'failed'
                    db.session.commit()
                print(f"❌ Resume parsing error: {e}")
        
        thread = threading.Thread(target=parse_resume_async)
        thread.daemon = True
        thread.start()
        
        flash('Resume uploaded successfully! Parsing in progress...', 'success')
        return redirect(url_for('resume_based_profile'))
    else:
        flash('Invalid file type. Please upload PDF, DOC, or DOCX files.', 'error')
        return redirect(url_for('upload'))

@app.route('/profile')
@login_required
def profile():
    return redirect(url_for('website_account_profile'))

@app.route('/update-profile', methods=['POST'])
@login_required
def update_profile():
    try:
        current_user.full_name = request.form.get('full_name', current_user.full_name)
        current_user.phone = request.form.get('phone', current_user.phone)
        current_user.skills = request.form.get('skills', current_user.skills)
        current_user.experience = request.form.get('experience', current_user.experience)
        current_user.preferred_titles = request.form.get('preferred_titles', current_user.preferred_titles)
        current_user.preferred_locations = request.form.get('locations', current_user.preferred_locations)
        
        db.session.commit()
        flash('Profile updated successfully!', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Error updating profile. Please try again.', 'error')
    
    return redirect(url_for('website_account_profile'))

@app.route('/websites')
@login_required
def websites():
    job_websites = JobWebsite.query.filter_by(user_id=current_user.id).all()
    
    # Create defaults if none exist
    if not job_websites:
        create_default_websites(current_user.id)
        job_websites = JobWebsite.query.filter_by(user_id=current_user.id).all()
    
    websites_data = [
        {
            'name': w.name,
            'url': w.url,
            'active': w.active,
            'icon': w.icon,
            'automation_enabled': w.automation_enabled,
            'last_scraped': w.last_scraped.strftime('%Y-%m-%d %H:%M') if w.last_scraped else 'Never',
            'jobs_found': w.jobs_found
        }
        for w in job_websites
    ]
    
    return render_template('websites.html', websites=websites_data, current_page='websites')

@app.route('/toggle-website', methods=['POST'])
@login_required
def toggle_website():
    if not request.json:
        return jsonify({'success': False, 'error': 'No JSON data provided'})
    
    website_name = request.json.get('website')
    if not website_name:
        return jsonify({'success': False, 'error': 'Website name not provided'})
    
    website = JobWebsite.query.filter_by(user_id=current_user.id, name=website_name).first()
    if website:
        website.active = not website.active
        db.session.commit()
        
        active_count = JobWebsite.query.filter_by(user_id=current_user.id, active=True).count()
        return jsonify({'success': True, 'active_count': active_count})
    
    return jsonify({'success': False, 'error': 'Website not found'})

@app.route('/toggle-automation', methods=['POST'])
@login_required
def toggle_automation():
    if not request.json:
        return jsonify({'success': False, 'error': 'No JSON data provided'})
    
    website_name = request.json.get('website')
    if not website_name:
        return jsonify({'success': False, 'error': 'Website name not provided'})
    
    website = JobWebsite.query.filter_by(user_id=current_user.id, name=website_name).first()
    if website:
        website.automation_enabled = not website.automation_enabled
        db.session.commit()
        
        return jsonify({'success': True, 'automation_enabled': website.automation_enabled})
    
    return jsonify({'success': False, 'error': 'Website not found'})

@app.route('/add-website', methods=['POST'])
@login_required
def add_website():
    name = request.form.get('website_name')
    url = request.form.get('website_url')
    
    if name and url:
        website = JobWebsite(
            user_id=current_user.id,
            name=name,
            url=url,
            active=True,
            automation_enabled=True
        )
        db.session.add(website)
        db.session.commit()
        flash('Website added successfully!', 'success')
    else:
        flash('Please provide both name and URL', 'error')
    
    return redirect(url_for('websites'))

@app.route('/automate')
@login_required
def automate():
    user_data = {
        'full_name': current_user.full_name or '',
        'email': current_user.email,
        'phone': current_user.phone or '',
        'skills': current_user.skills or 'Python, Web Scraping, Flask, Automation',
        'experience': current_user.experience or '2+ years in Python and automation scripting',
        'preferred_titles': current_user.preferred_titles or 'Python Developer, Web Scraper',
        'locations': current_user.preferred_locations or 'Remote, Karachi'
    }
    
    active_websites = JobWebsite.query.filter_by(user_id=current_user.id, active=True).all()
    automation_websites = JobWebsite.query.filter_by(user_id=current_user.id, active=True, automation_enabled=True).all()
    
    active_websites_data = [{'name': w.name, 'url': w.url} for w in active_websites]
    automation_websites_data = [{'name': w.name, 'url': w.url, 'jobs_found': w.jobs_found} for w in automation_websites]
    
    return render_template('automate.html', 
                         user_data=user_data, 
                         active_websites=active_websites_data,
                         automation_websites=automation_websites_data,
                         automation_status=current_user.automation_status,
                         current_page='automate')

@app.route('/start-automation', methods=['POST'])
@login_required
def start_automation():
    try:
        if current_user.automation_status == 'running':
            flash('Automation is already running. Please wait for it to complete.', 'warning')
            return redirect(url_for('automate'))
        
        # Check if user has required data
        if not current_user.skills or not current_user.preferred_titles:
            flash('Please complete your profile before starting automation.', 'error')
            return redirect(url_for('resume_based_profile'))
        
        # Check if there are active websites with automation enabled
        automation_websites = JobWebsite.query.filter_by(
            user_id=current_user.id, 
            active=True, 
            automation_enabled=True
        ).count()
        
        if automation_websites == 0:
            flash('Please enable automation for at least one website.', 'error')
            return redirect(url_for('websites'))
        
        # Start automation in background
        run_automation_async(current_user.id)
        
        flash('Job automation started! This may take a few minutes. Check back soon for results.', 'success')
        return redirect(url_for('automation_status'))
        
    except Exception as e:
        flash(f'Error starting automation: {str(e)}', 'error')
        return redirect(url_for('automate'))

@app.route('/automation-status')
@login_required
def automation_status():
    """Real-time automation status page"""
    applications_count = Application.query.filter_by(user_id=current_user.id).count()
    recent_applications = Application.query.filter_by(user_id=current_user.id)\
                                          .order_by(Application.applied_at.desc())\
                                          .limit(5).all()
    
    return render_template('automation_status.html',
                         automation_status=current_user.automation_status,
                         applications_count=applications_count,
                         recent_applications=recent_applications,
                         current_page='automate')

@app.route('/api/automation-status')
@login_required
def api_automation_status():
    """API endpoint for real-time status updates"""
    return jsonify({
        'status': current_user.automation_status,
        'applications_count': Application.query.filter_by(user_id=current_user.id).count(),
        'websites_scraped': JobWebsite.query.filter_by(user_id=current_user.id)\
                                           .filter(JobWebsite.last_scraped.isnot(None)).count()
    })

@app.route('/jobs')
@login_required
def jobs():
    applications = Application.query.filter_by(user_id=current_user.id)\
                                  .order_by(Application.applied_at.desc()).all()
    
    applied_jobs = [
        {
            'title': app.title,
            'company': app.company,
            'link': app.job_url,
            'date': app.applied_at.strftime('%Y-%m-%d'),
            'status': app.status,
            'website': app.website_name or 'Unknown',
            'location': app.location or 'Not specified',
            'salary': app.salary_range or 'Not specified',
            'match_score': round(app.match_score, 1) if app.match_score else 0,
            'description': app.job_description[:200] + '...' if app.job_description and len(app.job_description) > 200 else app.job_description
        }
        for app in applications
    ]
    
    # Statistics
    stats = {
        'total': len(applied_jobs),
        'applied': len([j for j in applied_jobs if j['status'] == 'Applied']),
        'found': len([j for j in applied_jobs if j['status'] == 'Found']),
        'avg_match': round(sum([j['match_score'] for j in applied_jobs]) / len(applied_jobs), 1) if applied_jobs else 0
    }
    
    return render_template('jobs.html', 
                         jobs=applied_jobs, 
                         stats=stats,
                         current_page='jobs')

@app.route('/job-profile')
@login_required
def job_profile():
    """Job Profile page with comprehensive fields"""
    # Get or create job profile
    profile = JobProfile.get_or_create_for_user(current_user.id)
    
    # Get latest resume
    latest_resume = Resume.query.filter_by(user_id=current_user.id).order_by(Resume.uploaded_at.desc()).first()
    
    # Add display properties for JSON fields
    profile.technical_skills_display = ', '.join(json.loads(profile.technical_skills)) if profile.technical_skills else ''
    profile.programming_languages_display = ', '.join(json.loads(profile.programming_languages)) if profile.programming_languages else ''
    profile.frameworks_libraries_display = ', '.join(json.loads(profile.frameworks_libraries)) if profile.frameworks_libraries else ''
    profile.databases_display = ', '.join(json.loads(profile.databases)) if profile.databases else ''
    profile.tools_software_display = ', '.join(json.loads(profile.tools_software)) if profile.tools_software else ''
    profile.cloud_platforms_display = ', '.join(json.loads(profile.cloud_platforms)) if profile.cloud_platforms else ''
    profile.operating_systems_display = ', '.join(json.loads(profile.operating_systems)) if profile.operating_systems else ''
    profile.methodologies_display = ', '.join(json.loads(profile.methodologies)) if profile.methodologies else ''
    profile.soft_skills_display = ', '.join(json.loads(profile.soft_skills)) if profile.soft_skills else ''
    profile.preferred_job_titles_display = ', '.join(json.loads(profile.preferred_job_titles)) if profile.preferred_job_titles else ''
    profile.preferred_industries_display = ', '.join(json.loads(profile.preferred_industries)) if profile.preferred_industries else ''
    profile.preferred_locations_display = ', '.join(json.loads(profile.preferred_locations)) if profile.preferred_locations else ''
    profile.job_type_preference_display = ', '.join(json.loads(profile.job_type_preference)) if profile.job_type_preference else ''
    profile.languages_spoken_display = ', '.join([f"{lang['language']} ({lang['proficiency']})" for lang in json.loads(profile.languages_spoken)]) if profile.languages_spoken else ''
    
    return render_template('job_profile.html', 
                         profile=profile, 
                         latest_resume=latest_resume,
                         current_page='job_profile')

@app.route('/account-profile')
@login_required
def account_profile():
    """Account Profile page for login credentials"""
    # Get statistics
    resumes_count = Resume.query.filter_by(user_id=current_user.id).count()
    applications_count = Application.query.filter_by(user_id=current_user.id).count()
    websites_count = JobWebsite.query.filter_by(user_id=current_user.id, active=True).count()
    
    return render_template('account_profile.html',
                         resumes_count=resumes_count,
                         applications_count=applications_count,
                         websites_count=websites_count,
                         current_page='account_profile')

@app.route('/update-job-profile', methods=['POST'])
@login_required
def update_job_profile():
    """Update comprehensive job profile"""
    try:
        profile = JobProfile.get_or_create_for_user(current_user.id)
        
        # Check if this is a draft save
        is_draft = request.form.get('save_as_draft') == 'true'
        
        # Personal Information
        profile.first_name = request.form.get('first_name', '').strip()
        profile.middle_name = request.form.get('middle_name', '').strip()
        profile.last_name = request.form.get('last_name', '').strip()
        profile.full_name = f"{profile.first_name} {profile.middle_name} {profile.last_name}".strip()
        profile.date_of_birth = request.form.get('date_of_birth') or None
        profile.nationality = request.form.get('nationality', '').strip()
        profile.gender = request.form.get('gender', '').strip()
        profile.marital_status = request.form.get('marital_status', '').strip()
        
        # Contact Information
        profile.email = request.form.get('email', '').strip()
        profile.phone_primary = request.form.get('phone_primary', '').strip()
        profile.phone_secondary = request.form.get('phone_secondary', '').strip()
        profile.address_line1 = request.form.get('address_line1', '').strip()
        profile.address_line2 = request.form.get('address_line2', '').strip()
        profile.city = request.form.get('city', '').strip()
        profile.state_province = request.form.get('state_province', '').strip()
        profile.postal_code = request.form.get('postal_code', '').strip()
        profile.country = request.form.get('country', '').strip()
        
        # Professional Summary
        profile.professional_summary = request.form.get('professional_summary', '').strip()
        profile.career_objective = request.form.get('career_objective', '').strip()
        profile.years_of_experience = int(request.form.get('years_of_experience')) if request.form.get('years_of_experience') else None
        profile.current_position = request.form.get('current_position', '').strip()
        profile.current_company = request.form.get('current_company', '').strip()
        profile.current_salary = request.form.get('current_salary', '').strip()
        
        # Skills (convert comma-separated to JSON)
        def parse_skills(field_value):
            if field_value:
                skills = [skill.strip() for skill in field_value.split(',') if skill.strip()]
                return json.dumps(skills) if skills else None
            return None
        
        profile.technical_skills = parse_skills(request.form.get('technical_skills'))
        profile.programming_languages = parse_skills(request.form.get('programming_languages'))
        profile.frameworks_libraries = parse_skills(request.form.get('frameworks_libraries'))
        profile.databases = parse_skills(request.form.get('databases'))
        profile.tools_software = parse_skills(request.form.get('tools_software'))
        profile.cloud_platforms = parse_skills(request.form.get('cloud_platforms'))
        profile.operating_systems = parse_skills(request.form.get('operating_systems'))
        profile.methodologies = parse_skills(request.form.get('methodologies'))
        profile.soft_skills = parse_skills(request.form.get('soft_skills'))
        
        # Job Preferences
        profile.preferred_job_titles = parse_skills(request.form.get('preferred_job_titles'))
        profile.preferred_industries = parse_skills(request.form.get('preferred_industries'))
        profile.preferred_locations = parse_skills(request.form.get('preferred_locations'))
        profile.remote_work_preference = request.form.get('remote_work_preference', '').strip()
        profile.job_type_preference = parse_skills(request.form.get('job_type_preference'))
        
        # Salary
        profile.expected_salary_min = int(request.form.get('expected_salary_min')) if request.form.get('expected_salary_min') else None
        profile.expected_salary_max = int(request.form.get('expected_salary_max')) if request.form.get('expected_salary_max') else None
        profile.salary_currency = request.form.get('salary_currency', 'USD')
        
        # Other preferences
        profile.availability_start_date = request.form.get('availability_start_date') or None
        profile.willing_to_travel = request.form.get('willing_to_travel', '').strip()
        profile.willing_to_relocate = request.form.get('willing_to_relocate') == 'true'
        profile.work_authorization = request.form.get('work_authorization', '').strip()
        
        # Social & Online Presence
        profile.linkedin_url = request.form.get('linkedin_url', '').strip()
        profile.github_url = request.form.get('github_url', '').strip()
        profile.portfolio_url = request.form.get('portfolio_url', '').strip()
        profile.personal_website = request.form.get('personal_website', '').strip()
        profile.twitter_url = request.form.get('twitter_url', '').strip()
        profile.stackoverflow_url = request.form.get('stackoverflow_url', '').strip()
        
        # Languages (parse format: "English (Native), Spanish (Fluent)")
        languages_input = request.form.get('languages_spoken', '').strip()
        if languages_input:
            languages = []
            for lang_entry in languages_input.split(','):
                lang_entry = lang_entry.strip()
                if '(' in lang_entry and ')' in lang_entry:
                    lang = lang_entry.split('(')[0].strip()
                    proficiency = lang_entry.split('(')[1].split(')')[0].strip()
                    languages.append({"language": lang, "proficiency": proficiency})
                else:
                    languages.append({"language": lang_entry, "proficiency": "Not specified"})
            profile.languages_spoken = json.dumps(languages) if languages else None
        
        # Additional Information
        profile.hobbies_interests = request.form.get('hobbies_interests', '').strip()
        profile.additional_information = request.form.get('additional_information', '').strip()
        profile.cover_letter_template = request.form.get('cover_letter_template', '').strip()
        
        # Settings
        profile.auto_update_enabled = request.form.get('auto_update_enabled') == 'true'
        
        # Calculate completion score
        profile.calculate_completion_score()
        
        # Update timestamp
        profile.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        if is_draft:
            return jsonify({'success': True, 'message': 'Draft saved successfully'})
        else:
            flash('Job profile updated successfully!', 'success')
            return redirect(url_for('resume_based_profile'))
        
    except Exception as e:
        db.session.rollback()
        print(f"Error updating job profile: {e}")
        if is_draft:
            return jsonify({'success': False, 'error': str(e)})
        else:
            flash('Error updating profile. Please try again.', 'error')
            return redirect(url_for('resume_based_profile'))

@app.route('/update-account-profile', methods=['POST'])
@login_required
def update_account_profile():
    """Update account profile (login credentials and basic info)"""
    try:
        current_user.full_name = request.form.get('full_name', '').strip()
        current_user.phone = request.form.get('phone', '').strip()
        current_user.location = request.form.get('location', '').strip()

        db.session.commit()
        flash('Account profile updated successfully!', 'success')

    except Exception as e:
        db.session.rollback()
        flash('Error updating account. Please try again.', 'error')

    return redirect(url_for('website_account_profile'))

@app.route('/profile-management', endpoint='profile_management')
@login_required
def profile_management():
    """Unified profile management page - redirect to website account profile"""
    return redirect(url_for('website_account_profile'))

@app.route('/website-account-profile')
@login_required
def website_account_profile():
    """Website Account Profile page"""
    # Get statistics
    resumes_count = Resume.query.filter_by(user_id=current_user.id).count()
    applications_count = Application.query.filter_by(user_id=current_user.id).count()
    websites_count = JobWebsite.query.filter_by(user_id=current_user.id, active=True).count()

    return render_template('website_account_profile.html',
                         resumes_count=resumes_count,
                         applications_count=applications_count,
                         websites_count=websites_count,
                         current_page='website_account_profile')

@app.route('/resume-based-profile')
@login_required
def resume_based_profile():
    """Resume-Based Profile page"""
    # Get job profile
    job_profile = JobProfile.query.filter_by(user_id=current_user.id).first()

    # Get latest resume
    latest_resume = Resume.query.filter_by(user_id=current_user.id).order_by(Resume.uploaded_at.desc()).first()

    # Add display properties for job profile
    if job_profile:
        job_profile.technical_skills_display = ', '.join(json.loads(job_profile.technical_skills)) if job_profile.technical_skills else ''
        job_profile.preferred_job_titles_display = ', '.join(json.loads(job_profile.preferred_job_titles)) if job_profile.preferred_job_titles else ''
        job_profile.preferred_locations_display = ', '.join(json.loads(job_profile.preferred_locations)) if job_profile.preferred_locations else ''

    return render_template('resume_based_profile.html',
                         job_profile=job_profile,
                         latest_resume=latest_resume,
                         current_page='resume_based_profile')

@app.route('/update-job-profile-simple', methods=['POST'])
@login_required
def update_job_profile_simple():
    """Update simplified job profile from resume data"""
    try:
        # Get or create job profile
        job_profile = JobProfile.get_or_create_for_user(current_user.id)

        # Helper function to parse comma-separated skills
        def parse_skills(field_value):
            if field_value:
                skills = [skill.strip() for skill in field_value.split(',') if skill.strip()]
                return json.dumps(skills) if skills else None
            return None

        # Update fields
        job_profile.technical_skills = parse_skills(request.form.get('technical_skills'))
        job_profile.professional_summary = request.form.get('experience_summary', '').strip()
        job_profile.preferred_job_titles = parse_skills(request.form.get('preferred_job_titles'))
        job_profile.preferred_locations = parse_skills(request.form.get('preferred_locations'))

        # Update legacy user fields for compatibility
        current_user.skills = request.form.get('technical_skills', '').strip()
        current_user.experience = request.form.get('experience_summary', '').strip()
        current_user.preferred_titles = request.form.get('preferred_job_titles', '').strip()
        current_user.preferred_locations = request.form.get('preferred_locations', '').strip()

        # Calculate completion score
        job_profile.calculate_completion_score()
        job_profile.updated_at = datetime.utcnow()

        db.session.commit()
        flash('Resume profile updated successfully!', 'success')

    except Exception as e:
        db.session.rollback()
        print(f"Error updating job profile: {e}")
        flash('Error updating profile. Please try again.', 'error')

    return redirect(url_for('resume_based_profile'))

@app.route('/change-password', methods=['POST'])
@login_required
def change_password():
    """Change user password"""
    try:
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        # Validate current password
        if not current_user.check_password(current_password):
            flash('Current password is incorrect', 'error')
            return redirect(url_for('website_account_profile'))

        # Validate new password
        if new_password != confirm_password:
            flash('New passwords do not match', 'error')
            return redirect(url_for('website_account_profile'))

        is_strong, message = is_strong_password(new_password)
        if not is_strong:
            flash(message, 'error')
            return redirect(url_for('website_account_profile'))
        
        # Update password
        current_user.set_password(new_password)
        db.session.commit()
        
        flash('Password changed successfully!', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('Error changing password. Please try again.', 'error')
    
    return redirect(url_for('website_account_profile'))

@app.route('/reset-database')
def reset_database():
    """Emergency route to reset database - remove in production"""
    try:
        # Delete database file
        if os.path.exists(database_path):
            os.remove(database_path)
        
        # Recreate database
        with app.app_context():
            db.create_all()
        
        return "Database reset successfully! <a href='/'>Go to home</a>"
    except Exception as e:
        return f"Error resetting database: {e}"

# Initialize database and create admin user
def init_app():
    """Initialize the application"""
    with app.app_context():
        # Run migration first
        migrate_database()
        
        # Create all tables
        db.create_all()
        
        # Create admin user if it doesn't exist
        try:
            admin = User.query.filter_by(email='<EMAIL>').first()
            if not admin:
                admin = User(
                    email='<EMAIL>',
                    full_name='Admin User',
                    skills='Python, Flask, JavaScript, React, SQL, Docker, Machine Learning, Data Science',
                    experience='5+ years of full-stack development experience with expertise in automation and AI',
                    preferred_titles='Senior Developer, Tech Lead, Full Stack Engineer, AI Engineer',
                    preferred_locations='Remote, San Francisco, New York, London',
                    resume_parsed=True
                )
                admin.set_password('Admin123!')
                
                db.session.add(admin)
                db.session.commit()
                
                # Create default websites for admin
                create_default_websites(admin.id)
                
                print("✅ Admin user created: <EMAIL> / Admin123!")
            else:
                print("✅ Admin user already exists")
                
        except Exception as e:
            print(f"⚠️  Admin user creation: {e}")

if __name__ == '__main__':
    init_app()
    print("🚀 Starting AI Job Application Assistant - Phase 2...")
    print("📧 Admin login: <EMAIL> / Admin123!")
    print("🌐 Visit: http://localhost:5000")
    print("🤖 Features: Real Resume Parsing + Browser Automation")
    print("🔄 If you have database issues, visit: http://localhost:5000/reset-database")
    app.run(debug=True, host='0.0.0.0', port=5000)
